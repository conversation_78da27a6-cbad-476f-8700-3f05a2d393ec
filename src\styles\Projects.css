/* Projects Page Styles */
.projects-page {
  padding: var(--space-24) 0;
  min-height: calc(100vh - 80px);
  background: var(--primary-bg);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.page-header h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Filters Section */
.filters-section {
  margin-bottom: var(--space-12);
  background: var(--card-bg);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-group label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group select,
.search-input input {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-normal);
}

.filter-group select:focus,
.search-input input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.1);
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input svg {
  position: absolute;
  left: var(--space-3);
  color: var(--text-muted);
  pointer-events: none;
}

.search-input input {
  padding-left: var(--space-10);
  width: 100%;
}

.filter-actions {
  display: flex;
  align-items: end;
}

.clear-filters {
  padding: var(--space-3) var(--space-6);
  background: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.clear-filters:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Error and No Results States */
.error-message,
.no-projects {
  text-align: center;
  padding: var(--space-16) var(--space-8);
  color: var(--text-secondary);
}

.no-projects svg {
  color: var(--accent-primary);
  margin-bottom: var(--space-4);
  opacity: 0.6;
}

.no-projects h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.no-projects p {
  font-size: var(--text-base);
  max-width: 400px;
  margin: 0 auto var(--space-6);
  line-height: 1.6;
}

/* Results Info */
.results-info {
  text-align: center;
  margin-top: var(--space-8);
  color: var(--text-muted);
  font-size: var(--text-sm);
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-16);
  justify-content: center;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

/* Project Card */
.project-card {
  background: var(--secondary-bg);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
  position: relative;
}

.project-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

/* Project Image */
.project-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
}

.project-status {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Project Content */
.project-content {
  padding: var(--space-6);
}

.project-header {
  margin-bottom: var(--space-4);
}

.project-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.project-location {
  color: var(--accent-primary);
  font-size: var(--text-base);
  font-weight: 500;
  margin-bottom: var(--space-1);
}

.project-type {
  display: inline-block;
  background: var(--glass-bg);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  border: 1px solid var(--border-secondary);
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

/* Progress Bar */
.project-progress {
  margin-bottom: var(--space-6);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.progress-bar {
  height: 8px;
  background: var(--border-primary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent);
  animation: shimmer 2s infinite;
}

/* Project Details */
.project-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.detail-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 600;
}

/* Project Features */
.project-features {
  margin-bottom: var(--space-6);
}

.project-features h4 {
  font-size: var(--text-base);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  font-weight: 600;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.feature-tag {
  background: var(--accent-secondary);
  color: var(--primary-bg);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: 500;
}

.feature-tag.more {
  background: var(--glass-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary);
}

/* Project CTA */
.project-cta {
  width: 100%;
  padding: var(--space-3) var(--space-6);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.project-cta:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects-page {
    padding: var(--space-16) 0;
  }

  .page-title {
    font-size: var(--text-4xl);
  }

  .page-subtitle {
    font-size: var(--text-lg);
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .project-card {
    max-width: 500px;
    margin: 0 auto;
  }

  .project-details {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: var(--text-3xl);
  }

  .project-content {
    padding: var(--space-4);
  }

  .project-image {
    height: 200px;
  }
}