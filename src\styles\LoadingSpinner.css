/* Loading Spinner Styles */
.loading-spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  position: relative;
  margin-bottom: var(--space-4);
}

.spinner-inner {
  width: 40px;
  height: 40px;
  border: 2px solid transparent;
  border-top: 2px solid var(--accent-secondary);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: spin-reverse 0.8s linear infinite;
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--text-lg);
  font-weight: 500;
}

@keyframes spin-reverse {
  from {
    transform: translate(-50%, -50%) rotate(360deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(0deg);
  }
}

/* Inline Loading Styles */
.inline-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-8);
  color: var(--text-secondary);
}

.inline-loading.small {
  padding: var(--space-4);
}

.inline-loading.large {
  padding: var(--space-12);
}

.inline-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
}

.inline-loading.small .inline-spinner {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

.inline-loading.large .inline-spinner {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.inline-loading-text {
  font-size: var(--text-base);
  font-weight: 500;
}

.inline-loading.small .inline-loading-text {
  font-size: var(--text-sm);
}

.inline-loading.large .inline-loading-text {
  font-size: var(--text-lg);
}
